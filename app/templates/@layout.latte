<!doctype html>
<html lang="sk">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="{$author}">
    <title>{$title}</title>
    <meta name="description" content="{$SEO_VARS["seo_desc"] ?? ''}">
    <meta name="robots" content="{$robots}">
    <meta name="google-site-verification" content="ce8y-pltxh2eUgJMy-1mkDlYzT6JBrseC0JsdeAWikY">
    <meta name="facebook-domain-verification" content="aqovwj8av8eiz7dyp9dnyjp6ovt9hi">

    {$SEO_VARS["seo_html"] ?? ''|noescape}
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css" />
    <link rel="icon" type="image/png" href="{$basePath."assets/img/favicon32.png"|version}">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Advent+Pro:wght@300;400;700&family=Prompt:wght@100;200;300;400;500;700;800;900&family=Roboto:wght@400;700&display=swap" rel="stylesheet">


    <link rel="stylesheet" href="{$basePath}assets/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.3.5/jquery.fancybox.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/orestbida/cookieconsent@v3.0.0/dist/cookieconsent.css">
    <!-- fonts -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css"
          integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css" integrity="sha384-mzrmE5qonljUremFsqc01SB46JvROS7bZs3IO2EmfFsd15uHvIt+Y8vEf7N7fWAU" crossorigin="anonymous">
    <link rel="stylesheet" href="{$basePath."assets/css/material-inputs.css"|version}">
    <link rel="stylesheet" href="{$basePath."assets/css/style.css"|version}">
    {if $lg !== "sk"}
        <link rel="stylesheet" href="{$basePath."assets/css/style_".$lg.".css"}">
    {/if}
    <link rel="stylesheet" href="{$basePath."assets/css/cookieconsent.css"|version}">
    <link rel="stylesheet" href="{$basePath."assets/css/lazy-loading.css"|version}">


    <!-- scripts -->

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.3/umd/popper.min.js"
            integrity="sha384-vFJXuSJphROIrBnz7yo7oB41mKfc8JzQZiCq4NCceLEaO4IHwicKwpJf9c9IpFgh"
            crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"
            integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy"
            crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.3.5/jquery.fancybox.min.js"></script>
    <script src="assets/js/inViewport.js"></script>
    <script src="assets/js/jquery.MultiFile.min.js" type="text/javascript" language="javascript"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
    <script src="{$basePath."assets/js/headroom.js"|version}"></script>
    <script>$.fancybox.defaults.hash = false;</script>
    <script src="{$basePath}assets/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js"></script>
    <script src="{$basePath}assets/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.sk.min.js"></script>
    {if !isset($kioskMode)}
    <script src="{$basePath."assets/js/custom.js"|version}" type="text/javascript"></script>
    {/if}
    <script src="{$basePath."assets/js/lazy-loading.js"|version}" type="text/javascript"></script>
    {\Kubomikita\FormFactory::scripts()}

    <script>
		window.dataLayer = window.dataLayer || [];
		function gtag(){ dataLayer.push(arguments);}
		gtag('consent', 'default', {
			'ad_storage': 'denied',
			'ad_user_data': 'denied',
			'ad_personalization': 'denied',
			'analytics_storage': 'denied',
			'wait_for_update': 500
		});
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-167SNWY3S8"></script>
    <script type="text/plain" data-category="analytics">
		window.dataLayer = window.dataLayer || [];
		function gtag(){ dataLayer.push(arguments); }
		gtag('js', new Date());

		gtag('config', 'G-167SNWY3S8');
    </script>
</head>
<body class="position-relative">
<div id="overlay" data-padding="152" data-padding-orig="152">
    <div id="overlayBackground"></div>
    <div id="overlayContent">
        <div id="classicPopup" class="popup">
            <div class="toolbar">
                <div class="toolbar-name"></div>
                <a href="javascript:;" class="closeBtn"><span class="fa fa-times"></span></a>
            </div>
            <div class="popupContent"></div>
        </div>
    </div>
</div>

    <header id="site-header" {if isset($kioskMode)}class="position-relative"{/if}>
        <div n:class="!isset($kioskMode) ? 'container' : 'container-fluid'">
            <div class="row no-gutters align-items-center">
                <div class="col-10 col-lg-2">
                    <a class="navbar-brand py-1 d-flex align-items-center" href="./">
                        <img src="{$basePath}assets/img/logo.png" alt="{$author}" class="img-fluid">
                    </a>
                </div>
                <div class="col-lg-10 col-2" n:if="!isset($kioskMode)">
                    <nav class="navbar navbar-desktop navbar-expand-lg p-0">
                        <button class="navbar-toggler mx-auto" type="button" data-toggle="collapse"
                                data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                                aria-expanded="false" aria-label="Toggle navigation">
                            <div class="navbar-burger-icon">
                                <span></span> <span></span> <span></span> <span></span>
                            </div>
                        </button>
                        <div class="collapse navbar-collapse text-center responsive-menu" id="navbarSupportedContent">
                            <div class="container">

                                <ul class="navbar-nav w-100 align-items-center justify-content-end">
                                    {\Menu::get("top", true)|noescape}
                                    <li class="ml-3">
                                        <a href="javascript:;" onclick="javascript:scrollToId('chcete-predat');" class="btn btn-info">
                                            Chcete niečo predať ?
                                        </a>
                                    </li>
                                </ul>
                            </div>

                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </header>
<div class="slider" n:if="!isset($kioskMode)">
    <div>
        {$presenter->renderSlider("homepage","homepageCarousel", 1)}
    </div>
</div>
<div class="wrapper position-relative">
    <section id="content" {if $Page->module_id === 'search' || isset($kioskMode)}class="bg-gray"{/if}>
        <div n:class="!isset($kioskMode) ? 'container' : 'container-fluid'">
            <div n:class="cms-content, !isset($kioskMode) ? 'py-5' : 'py-3'">
                {$content|noescape}
            </div>
        </div>
    </section>

    {if $Page->main}


    {else}
        {if $presenter->sliderImage !== null}
            {var $style = 'background-image: url('.$presenter->sliderImage->src.');'}
        {elseif $Page->slider() !== null && $Page->slider()->image()->id !== null}
            {var $style = 'background: url('.$Page->slider()->image()->url().') no-repeat center;background-size: cover;'}
            {var $icon = (strlen(trim(($Page->slider()->image()->link ?? ''))) ? $Page->slider()->image()->link : null )}
        {/if}
        {*php dump($presenter,$style)
        <div class="page-header position-relative" style="{$style ?? ''|noescape}">
            <div class="page-header-overlay"></div>
            <div class="container">
                <h1 class="text-center">
                    <div n:ifset="$icon" class="mb-3">{svg $icon}</div>
                    {$pageHeading}
                </h1>
            </div>


        </div>*}

    {/if}
    {*<div id="space" {if !$Page->main}class="pt-5"{/if}>
        <h2 class="text-center pb-lg-4 pt-3 pt-lg-0"><span>prezrite si</span> naše priestory</h2>
        <div class="mt-5">
            {module gallery space}
        </div>
    </div>*}
    {if !isset($kioskMode)}
    <div class="projects py-5" n:if="$Page->main">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-auto"><h2><span>Objavte naše</span> najnovšie ponuky</h2></div>
                <div class="col-lg-auto ml-auto d-none d-lg-block">
                    <a href="{$basePath}predaj" class="btn btn-default tx-semibold">ZOBRAZIŤ VŠETKY PONUKY</a>
                </div>
            </div>

            {module search news}


            <div class="pt-3 d-block d-lg-none text-center">
                <a href="{$basePath}predaj" class="btn btn-default tx-semibold">ZOBRAZIŤ VŠETKY PONUKY</a>
            </div>

        </div>
    </div>
    <div class="projects py-5" n:if="$Page->main">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-auto"><h2><span>Objavte naše</span> developerské projekty</h2></div>
                <div class="col-lg-auto ml-auto d-none d-lg-block">
                    <a href="{$basePath}developerske-projekty" class="btn btn-default tx-semibold">ZOBRAZIŤ VŠETKY PROJEKTY</a>
                </div>
            </div>

            {module category projects}


            <div class="pt-3 d-block d-lg-none text-center">
                <a href="{$basePath}developerske-projekty" class="btn btn-default tx-semibold">ZOBRAZIŤ VŠETKY PROJEKTY</a>
            </div>

        </div>
    </div>

    <div class="buy py-5" id="chcete-predat">
        <div class="container">
            <h2><span>Chcete predať</span> alebo kúpiť nehnuteľnosť?</h2>

            {inputError saleForm}
            {form saleForm}
            <div class="row py-5 align-items-center">
                <div class="col-lg-4">
                    <div class="form-label-group">
                        {input meno}
                        {label meno}
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="form-label-group">
                        {input email}
                        {label email}
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="form-label-group">
                        {input telefon}
                        {label telefon}
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="form-label-group">
                        {input sprava}
                        {label sprava}
                    </div>
                </div>
                <div class="col-lg-auto">
                    <div class="form-check tx-upper tx-medium">
                        {input gdpr}
                        {label gdpr}

                    </div>
                </div>
                <div class="col-lg-auto ml-auto text-right">
                    {input submit}
                </div>
            </div>

            {/form}

        </div>
    </div>
    <div class="divisions py-5" id="kontakt">
        <div class="container">
            <h2><span>Máte záujem o stretnutie?</span> Radi Vás privítame v našich pobočkách.</h2>
            <div class="row py-4">
                <div class="col-lg-4">
                    <div class="box">
                        <div class="box-image">
                            <img src="{$basePath}assets/img/sl.jpg" class="img-fluid" alt="Stará Ľubovňa">
                            <div class="box-image-text">STARÁ ĽUBOVŇA</div>
                        </div>
                        <div class="box-desc">
                            <div class="row">
                                <div class="col-lg-12 mb-3">
                                    <div class="d-flex">
                                        <div>{svg "assets/img/marker.svg"}</div>
                                        <div class="ml-3">
                                            <div><strong>Adresa</strong></div>
                                            <p>Obchodná 1961/5<br> 06401 Stará Ľubovňa</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12 mb-3">
                                    <div class="d-flex">
                                        <div>{svg "assets/img/phone.svg"}</div>
                                        <div class="ml-3">
                                            <div><strong>Kontakty</strong></div>
                                            <p>
                                                Mobil: <a href="tel:+421918514766" class="tx-medium">0918 514 766</a> <br>
                                                E-mail: <a href="mailto:<EMAIL>" class="tx-medium"><EMAIL></a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <a class="btn btn-info" href="{$basePath}pobocka-stara-lubovna">Viac o pobočke</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">

                    <div class="box">
                        <div class="box-image">
                            <img src="{$basePath}assets/img/po.jpg" class="img-fluid" alt="Stará Ľubovňa">
                            <div class="box-image-text">PREŠOV</div>
                        </div>
                        <div class="box-desc">
                            <div class="row">
                                <div class="col-lg-12 mb-3">
                                    <div class="d-flex">
                                        <div>{svg "assets/img/marker.svg"}</div>
                                        <div class="ml-3">
                                            <div><strong>Adresa</strong></div>
                                            <p>Slovenská 3263/8<br> 08001 Prešov</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12 mb-3">
                                    <div class="d-flex">
                                        <div>{svg "assets/img/phone.svg"}</div>
                                        <div class="ml-3">
                                            <div><strong>Kontakty</strong></div>
                                            <p>
                                                Mobil: <a href="tel:+421905160120" class="tx-medium">0905 160 120</a> <br>
                                                E-mail: <a href="mailto:<EMAIL>" class="tx-medium"><EMAIL></a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <a class="btn btn-info" href="{$basePath}pobocka-presov">Viac o pobočke</a>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="col-lg-4">

                    <div class="box">
                        <div class="box-image">
                            <img src="{$basePath}assets/img/ke.jpg" class="img-fluid" alt="Stará Ľubovňa">
                            <div class="box-image-text">KOŠICE</div>
                        </div>
                        <div class="box-desc">
                            <div class="row">
                                <div class="col-lg-12 mb-3">
                                    <div class="d-flex">
                                        <div>{svg "assets/img/marker.svg"}</div>
                                        <div class="ml-3">
                                            <div><strong>Adresa</strong></div>
                                            <p>Gemerská 2068/3<br> 04011 Košice</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12 mb-3">
                                    <div class="d-flex">
                                        <div>{svg "assets/img/phone.svg"}</div>
                                        <div class="ml-3">
                                            <div><strong>Kontakty</strong></div>
                                            <p>
                                                Mobil: <a href="tel:+421903367922" class="tx-medium">0903 367 922</a> <br>
                                                E-mail: <a href="mailto:<EMAIL>" class="tx-medium"><EMAIL></a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <a class="btn btn-info" href="{$basePath}pobocka-kosice">Viac o pobočke</a>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    {/if}
    <footer class="pt-5" n:if="!isset($kioskMode)">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="mt-3 mb-5">
                        <img src="{$basePath}assets/img/logo_alpha.png" alt="{$author}" class="img-fluid">
                        <img src="{$basePath}assets/img/logo-overeny.png" class="img-fluid">
                    </div>

                    <h3><span>Chcete ostať v obraze?</span> Prihláste sa na odber noviniek</h3>
                    {inputError newsletterForm}
                    {form newsletterForm}

                        <div class="row no-gutters mb-3 mb-lg-0">
                            <div class="col-lg">
                            <div class="form-label-group">
                                {input email}
                            </div>
                            </div>
                            <div class="col-lg-auto">
                            {input submit}
                            </div>
                        </div>

                        <div class="form-check tx-upper tx-medium">
                            {input gdpr}
                            {label gdpr}

                        </div>

                    {/form}
                    <div class="copy text-left pt-3 mt-4 mt-lg-5">
                        {__("Všetky práva vyhradené")} &copy; 2010 - {date("Y")} {$author}  | <a href="https://www.backoffice.sk/prihlasenie" target="_blank">Prihlásenie backOffice</a> | <a href="javascript;" type="button" data-cc="show-preferencesModal">Nastavenie cookies</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

</div>

<script n:if="!$Page->main && $Page->module_id !== 'brokers'">
    setTimeout(function (){
	    scrollToId('content');
    }, 300);
</script>

<script defer src="https://cdn.jsdelivr.net/gh/orestbida/cookieconsent@v3.0.0/dist/cookieconsent.umd.js"></script>
<script defer src="{$basePath}assets/js/cookieconsent-config.js?v=1.0003"></script>

{*
<script n:syntax="double">(g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.${c}apis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})
	({key: "AIzaSyApJYHtn_myGf-Cp59xAZ67fTXWQIGXESU", v: "weekly"});</script>*}
{*}
<script async
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyApJYHtn_myGf-Cp59xAZ67fTXWQIGXESU&loading=async&callback=initMap">
</script>*}
</body>
</html>