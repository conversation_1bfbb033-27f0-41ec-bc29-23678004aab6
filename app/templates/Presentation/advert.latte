<div class="advert-detail">
    <div class="row">
        <div class="col-lg-6">
            {foreach $images as $image}
                {varType App\Model\Adverts\AdvertsImages $image}
                <a href="{$image->src}" data-fancybox="advert"><img src="{$image->resizedCachedUrl(width: 1600,height: 1200)}" class="img-fluid rounded" alt="{$advert->title_sk}"></a>
                {php break}
            {/foreach}
            {*<div class="row no-gutters advert-images my-2" n:if="count($images) > 1">
                {foreach $images as $image}
                    {varType App\Model\Adverts\AdvertsImages $image}
                    {continueIf $iterator->isFirst()}
                    <div class="col-2 pr-1 pb-1">
                        <a href="{$image->src}" data-fancybox="advert"><img src="{$image->resizedCachedUrl(width: 800,height: 600)}" class="img-fluid" alt="{$advert->title_sk}"></a>
                    </div>
                {/foreach}
            </div>*}
        </div>
        <div class="col-lg-6">
            {varType App\Model\Adverts\Adverts $advert}
            <div class="row no-gutters justify-content-between">
                <div class="col-lg-auto">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mt-0 p-1">
                            <li class="breadcrumb-item"><a href="{$basePath}"><i class="fas fa-home"></i></a></li>
                            <li class="breadcrumb-item" n:foreach="$breadcrumb as $link => $name"><a href="{$link}">{$name}</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{$advert->title_sk}</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-lg-auto">
                    <div class="box-badges mb-2" n:if="($badges = $advert->getBadges()) !== []">
                        <span class="badge {$class} tx-upper" n:foreach="$badges as $name => $class">{$name}</span>
                    </div>
                </div>
            </div>
            <h2>{$advert->title_sk}</h2>
            <div class="row justify-content-between">
                <div class="col-auto">
                    <img src="{$basePath}assets/img/icon_place.svg" alt="Adresa">
                    {$advert->district()->value_sk},
                    {$advert->region()->value_sk},
                    {$advert->state()->value_sk}
                </div>
                <div class="col-auto">
                    <img src="{$basePath}assets/img/icon_house.svg" alt="Adresa">
                    {$advert->property_type()->value_sk}
                </div>
                <div class="col-auto">
                    <img src="{$basePath}assets/img/icon_tag.svg" alt="Adresa">
                    {$advert->transaction()['value']}
                </div>
            </div>

            <div class="advert-detail-price py-3">
                {$advert->price()}
            </div>

            <p class="advert-short-description">
                {$advert->description_sk}
            </p>

            <div class="py-3 d-flex justify-content-center">
                <div class="broker-home broker-advert-detail mb-3 mt-3 p-3 d-flex align-items-center px-5">
                    <div class="broker-image">
                        {var $image = $broker->image}
                        {if $broker->image === ""}
                            {var $image = \Core::$conf["imgNotFound"]}
                        {/if}
                        <a href="{$basePath}ponuky-makler-{$broker->backofficeid}" n:tag-if="$broker instanceof App\Model\Brokers">
                            <img src="{$image}" alt="{$broker->name}">
                        </a>
                    </div>
                    <div>
                        <div class="broker-name tx-medium ">
                            {$broker->name}
                        </div>
                        <div class="broker-position mb-3">{$broker->position}</div>
                        <div class="broker-phone"><a href="tel:{$broker->phone|replace:' ', ''}">{$broker->phone}</a></div>
                        <div class="broker-email"><a href="mailto:{$broker->email}">{$broker->email}</a></div>
                    </div>
                    {*<div class="mt-3" n:if="$broker instanceof App\Model\Brokers">
                        <a href="{$basePath}ponuky-makler-{$broker->backofficeid}" class="btn btn-info">Všetky ponuky</a>
                    </div>*}
                    {*php dump($broker->name, $broker)*}
                </div>
                {*<a class="btn btn-info" href="javascript:;" onclick="javascript:scrollToId('advertInfo')">VIAC O NEHNUTEĽNOSTI</a>*}
            </div>

        </div>
    </div>
</div>
