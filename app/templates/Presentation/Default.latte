<div id="presentation" class="carousel slide" data-ride="carousel" data-pause="false" data-interval="7000">
    <div class="carousel-inner">
        {foreach $adverts as $a}
            {var $advert = new \App\Model\Adverts\Adverts($a)}
            {var $broker = $advert->broker()}
            {var $images = $advert->images()}
            <div n:class="carousel-item, $iterator->isFirst() ? active">
                {include ./advert.latte, advert: $advert, images: $images, breadcrumb: [], broker: $broker}
            </div>
        {/foreach}
    </div>
</div>
