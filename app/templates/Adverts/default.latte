<div class="advert-detail">
    <div class="row">
        <div class="col-lg-5">
            {foreach $images as $full => $thumbnail}
                <a href="{$full}" data-fancybox="advert"><img src="{$thumbnail}" class="img-fluid rounded" alt="{$advert->title_sk}"></a>
                {php break}
            {/foreach}
            <div class="row no-gutters advert-images my-2" n:if="count($images) > 1">
                {foreach $images as $full => $thumbnail}
                    {continueIf $iterator->isFirst()}
                    <div class="col-2 pr-1 pb-1">
                        <a href="{$full}" data-fancybox="advert"><img src="{$thumbnail}" class="img-fluid" alt="{$advert->title_sk}"></a>
                    </div>
                {/foreach}
            </div>
        </div>
        <div class="col-lg-7">
            {varType App\Model\Adverts\Adverts $advert}
            <div class="row no-gutters justify-content-between">
                <div class="col-lg-auto">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mt-0 p-1">
                            <li class="breadcrumb-item"><a href="{$basePath}"><i class="fas fa-home"></i></a></li>
                            <li class="breadcrumb-item" n:foreach="$breadcrumb as $link => $name"><a href="{$link}">{$name}</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{$advert->title_sk}</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-lg-auto">
                    <div class="box-badges mb-2" n:if="($badges = $advert->getBadges()) !== []">
                        <span class="badge {$class} tx-upper" n:foreach="$badges as $name => $class">{$name}</span>
                    </div>
                </div>
            </div>
            <h2>{$advert->title_sk}</h2>
            <div class="row justify-content-between">
                <div class="col-auto">
                    <img src="{$basePath}assets/img/icon_place.svg" alt="Adresa">
                    {$advert->district()->value_sk},
                    {$advert->region()->value_sk},
                    {$advert->state()->value_sk}
                </div>
                <div class="col-auto">
                    <img src="{$basePath}assets/img/icon_house.svg" alt="Adresa">
                    {$advert->property_type()->value_sk}
                </div>
                <div class="col-auto">
                    <img src="{$basePath}assets/img/icon_tag.svg" alt="Adresa">
                    {$advert->transaction()['value']}
                </div>
            </div>

            <div class="advert-detail-price py-3">
                {$advert->price()}
            </div>

            <p class="advert-short-description">
                {$advert->description_sk}
            </p>

            <div class="py-3">
                <a class="btn btn-info" href="javascript:;" onclick="javascript:scrollToId('advertInfo')">VIAC O NEHNUTEĽNOSTI</a>
            </div>

        </div>
    </div>
</div>

</div>
</div>
<div id="advertInfo" class="advert-detail-info py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-7">
                {var $paramsCount = count($params)}
                {var $paramsBreakpoint = (int) ceil($paramsCount / 2)}
                <div class="row mb-3">
                    <div class="col-lg-6">
                        {foreach $params as $paramName => $paramValue}
                            <div class="row no-gutters border-bottom mb-2 pb-2">
                                <div class="col-6">{$paramName}</div>
                                <div class="col-6 text-right tx-bold">{$paramValue|noescape}</div>
                            </div>

                            {if $iterator->counter === $paramsBreakpoint}
                                </div><div class="col-lg-6">
                            {/if}
                        {/foreach}
                    </div>
                </div>
                <p class="text-justify">{$advert->description_sk|breakLines}</p>
                {if strlen(trim($advert->youtube)) > 0}
                    <iframe width="100%" height="480" src="https://www.youtube.com/embed/{$advert->youtube}" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                {/if}
            </div>
            <div class="col-lg-5">
                <script src="https://polyfill-fastly.io/v3/polyfill.min.js"></script>
                <script>
		            const items = JSON.parse({\Nette\Utils\Json::encode($mapItems)});
		            const handler = null;
					const zoom = 13;
					const center = { lat: {$mapCenter['lat']}, lng: {$mapCenter['lng']}};
                </script>
                <script type="module" src="{$basePath}assets/js/map.js"></script>
                <div id="map" style="width: 100%;height: 50vh"></div>

                <div class="broker-home broker-advert-detail mb-3 mt-3 p-3">
                    <div class="broker-image">
                        {var $image = $broker->image}
                        {if $broker->image === ""}
                            {var $image = \Core::$conf["imgNotFound"]}
                        {/if}
                        <a href="{$basePath}ponuky-makler-{$broker->backofficeid}" n:tag-if="$broker instanceof App\Model\Brokers">
                            <img src="{$image}" alt="{$broker->name}">
                        </a>
                    </div>
                    <div class="broker-name tx-medium ">
                        {$broker->name}
                    </div>
                    <div class="broker-position mb-3">{$broker->position}</div>
                    <div class="broker-phone"><a href="tel:{$broker->phone|replace:' ', ''}">{$broker->phone}</a></div>
                    <div class="broker-email"><a href="mailto:{$broker->email}">{$broker->email}</a></div>

                    <div class="mt-3" n:if="$broker instanceof App\Model\Brokers">
                        <a href="{$basePath}ponuky-makler-{$broker->backofficeid}" class="btn btn-info">Všetky ponuky</a>
                    </div>
                    {*php dump($broker->name, $broker)*}
                </div>
            </div>
        </div>

        <script n:syntax=off>(g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.${c}apis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})
		    ({key: "AIzaSyApJYHtn_myGf-Cp59xAZ67fTXWQIGXESU", v: "weekly"});</script>
