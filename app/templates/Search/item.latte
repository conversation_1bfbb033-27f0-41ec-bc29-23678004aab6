<div class="box mt-0">
    <div class="box-image">
        {var $titleImage = $advert->titleImage(true)}
        <a href="{$advert->getUrl()}">
            <img src="{$titleImage->resizedCachedUrl(App\Model\Adverts\AdvertsImages::RESIZE_CROP, 800, 600)}" class="img-fluid" alt="{$advert->title_sk}" n:if="$titleImage !== null">
            <img src="imgcache/assets/img/notfound_w800_h600_t5.jpg" n:if="$titleImage === null" class="img-fluid">
        </a>
        {*$advert->titleImage()?->resizedCachedUrl(App\Model\Adverts\AdvertsImages::RESIZE_LANDSCAPE, 800, 600)*}
    </div>

    <div class="box-desc pt-2">
        <div class="box-badges mb-2" n:if="($badges = $advert->getBadges()) !== []">
            <span class="badge {$class} tx-upper" n:foreach="$badges as $name => $class">{$name}</span>
        </div>
        <div class="box-desc-name">{$advert->title_sk}</div>
        <p>{\Format::shortText($advert->description_sk, 150)|stripHtml}</p>
        <div class="box-spacer"></div>
        {var $type = $advert->transaction()}
        <div class="row no-gutters align-items-center pb-3">
            <div class="col-auto type {$type['class']} tx-upper">{$type['value']}</div>
            <div class="col-auto price ml-auto text-right">{$advert->price()|noescape}</div>
        </div>
        <div class="box-detail-button">
            <a href="{$advert->getUrl()}" class="btn btn-info btn-block">Detail ponuky</a>
        </div>
    </div>
</div>