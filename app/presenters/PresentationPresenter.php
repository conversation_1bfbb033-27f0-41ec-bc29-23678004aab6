<?php

namespace CMSIzzy;

use App\Model\Adverts\Adverts;
use Nette\Application\BadRequestException;

class PresentationPresenter extends DefaultPresenter {

	public function actionDefault(): void
	{
		$this->template->kioskMode = true;
		$this->template->adverts = (new Adverts())->findAll()->where('show_advert = 1 AND in_archive = 0')->order('id DESC')->limit(500)->fetchAll();
	}
	public function actionPoprad(): void
	{
		$this->template->kioskMode = true;
		$this->template->adverts = (new Adverts())->findAll()->where('show_advert = 1 AND in_archive = 0 AND broker_id IN (SELECT backofficeid FROM backoffice_brokers WHERE pobockaid = 2)')->order('id DESC')->fetchAll();
	}
}