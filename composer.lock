{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "91e3a4e59258d5f3e11d58c3c964b048", "packages": [{"name": "contributte/console", "version": "v0.10.1", "source": {"type": "git", "url": "https://github.com/contributte/console.git", "reference": "dc2b84fb8dd795ea9988f396311aeed435aed495"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/console/zipball/dc2b84fb8dd795ea9988f396311aeed435aed495", "reference": "dc2b84fb8dd795ea9988f396311aeed435aed495", "shasum": ""}, "require": {"nette/di": "^3.1.8", "php": ">=8.1", "symfony/console": "^6.4.2 || ^7.0.2"}, "require-dev": {"contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.4", "mockery/mockery": "^1.6.7", "nette/http": "^3.2.3", "symfony/event-dispatcher": "^6.4.2 || ^7.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Console\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Best Symfony Console for Nette Framework", "homepage": "https://github.com/contributte/console", "keywords": ["console", "nette", "symfony"], "support": {"issues": "https://github.com/contributte/console/issues", "source": "https://github.com/contributte/console/tree/v0.10.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-01-04T20:10:58+00:00"}, {"name": "contributte/deployer-extension", "version": "dev-advholding", "source": {"type": "git", "url": "https://github.com/kubomikita/deployer-extension.git", "reference": "7c39627edc68b2409653648c4929f34be66f1ec7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kubomikita/deployer-extension/zipball/7c39627edc68b2409653648c4929f34be66f1ec7", "reference": "7c39627edc68b2409653648c4929f34be66f1ec7", "shasum": ""}, "require": {"dg/ftp-deployment": "^3.3.1", "nette/di": "^3.0.2", "php": ">=7.1"}, "require-dev": {"mockery/mockery": "~1.1.0", "ninjify/nunjuck": "^0.2.0", "ninjify/qa": "^0.8.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Contributte\\Deployer\\": "src"}}, "scripts": {"qa": ["linter src tests", "codesniffer src tests"], "tests": ["tester -s -p php --colors 1 -C tests/cases"], "coverage": ["tester -s -p phpdbg --colors 1 -C --coverage ./coverage.xml --coverage-src ./src tests/cases"], "phpstan-install": ["mkdir -p temp/phpstan", "composer require -d temp/phpstan phpstan/phpstan:^0.10", "composer require -d temp/phpstan phpstan/phpstan-deprecation-rules:^0.10", "composer require -d temp/phpstan phpstan/phpstan-nette:^0.10", "composer require -d temp/phpstan phpstan/phpstan-strict-rules:^0.10"], "phpstan": ["temp/phpstan/vendor/bin/phpstan analyse -l max -c phpstan.neon src"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Fork of Ftp-Deployment Extension for Nette", "homepage": "https://github.com/contributte/deployer-extension", "support": {"source": "https://github.com/kubomikita/deployer-extension/tree/advholding"}, "time": "2023-02-07T20:20:09+00:00"}, {"name": "dg/ftp-deployment", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/dg/ftp-deployment.git", "reference": "217c0199aaae3263e121c64ae3a8181ac6a32a3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/ftp-deployment/zipball/217c0199aaae3263e121c64ae3a8181ac6a32a3c", "reference": "217c0199aaae3263e121c64ae3a8181ac6a32a3c", "shasum": ""}, "require": {"ext-zlib": "*", "php": ">=8.0", "phpseclib/phpseclib": "^3.0"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12"}, "suggest": {"ext-ftp": "to connect to ftp:// server", "ext-json": "to preprocess CSS files via online service", "ext-openssl": "to connect to ftps:// server and preprocess CSS files", "ext-ssh2": "to connect to sftp:// server"}, "bin": ["deployment"], "type": "project", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "A tool for automated deployment of web applications to an FTP server.", "homepage": "https://github.com/dg/ftp-deployment", "keywords": ["deployment", "ftp", "ssh"], "support": {"issues": "https://github.com/dg/ftp-deployment/issues", "source": "https://github.com/dg/ftp-deployment/tree/v3.6.0"}, "time": "2023-02-28T21:30:17+00:00"}, {"name": "kubomikita/cms-core", "version": "dev-master", "dist": {"type": "path", "url": "../kubomikita/cms-core", "reference": "99700f9b016dc76c8f722fe0d2c5d3dd720fdb2c"}, "require": {"kubomikita/service-container": "^1.1.0", "latte/latte": "v2.11.*", "nette/nette": "^3.2", "php": ">= 8.1"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["GNU"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://www.jakubmikita.sk"}], "keywords": ["activerecord", "cms", "core", "izzy"], "support": {"issues": "https://github.com/kubomikita/cms-core/issues"}, "transport-options": {"symlink": true, "relative": true}}, {"name": "kubomikita/form-factory", "version": "dev-php8.1", "dist": {"type": "path", "url": "../kubomikita/form-factory", "reference": "a7790d6b09106e94a923ce2d7cb229a89b1c0158"}, "require": {"matthiasmullie/minify": "^1.3", "php": ">=8.0"}, "require-dev": {"latte/latte": "^2.4", "nette/reflection": "^2.4", "nette/utils": "^2.4 || ~3.0.0", "tracy/tracy": "^2.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "http://www.jakubmikita.sk"}], "description": "Form builder", "homepage": "http://www.jakubmikita.sk", "transport-options": {"symlink": true, "relative": true}}, {"name": "kubomikita/service-container", "version": "1.1.1", "source": {"type": "git", "url": "https://bitbucket.org/kubomikita/service-container.git", "reference": "186a1b3d1a2c5edc2c92865f62a8fd0181d95c43"}, "dist": {"type": "zip", "url": "https://bitbucket.org/kubomikita/service-container/get/186a1b3d1a2c5edc2c92865f62a8fd0181d95c43.zip", "reference": "186a1b3d1a2c5edc2c92865f62a8fd0181d95c43", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://www.jakubmikita.sk"}], "description": "Service container - simple registry design pattern", "homepage": "http://www.jakubmikita.sk", "support": {"source": "https://bitbucket.org/kubomikita/service-container/src/186a1b3d1a2c5edc2c92865f62a8fd0181d95c43/?at=1.1.1"}, "time": "2022-12-30T19:29:49+00:00"}, {"name": "latte/latte", "version": "v2.11.7", "source": {"type": "git", "url": "https://github.com/nette/latte.git", "reference": "0ac0843a459790d471821f6a82f5d13db831a0d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/latte/zipball/0ac0843a459790d471821f6a82f5d13db831a0d3", "reference": "0ac0843a459790d471821f6a82f5d13db831a0d3", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "php": "7.1 - 8.3"}, "conflict": {"nette/application": "<2.4.1"}, "require-dev": {"nette/php-generator": "^3.3.4", "nette/tester": "^2.0", "nette/utils": "^3.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.3"}, "suggest": {"ext-fileinfo": "to use filter |datastream", "ext-iconv": "to use filters |reverse, |substring", "ext-mbstring": "to use filters like lower, upper, capitalize, ...", "nette/php-generator": "to use tag {templatePrint}", "nette/utils": "to use filter |webalize"}, "bin": ["bin/latte-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.11-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "☕ Latte: the intuitive and fast template engine for those who want the most secure PHP sites. Introduces context-sensitive escaping.", "homepage": "https://latte.nette.org", "keywords": ["context-sensitive", "engine", "escaping", "html", "nette", "security", "template", "twig"], "support": {"issues": "https://github.com/nette/latte/issues", "source": "https://github.com/nette/latte/tree/v2.11.7"}, "time": "2023-10-18T17:16:11+00:00"}, {"name": "matthi<PERSON><PERSON><PERSON>/minify", "version": "1.3.71", "source": {"type": "git", "url": "https://github.com/matthiasmullie/minify.git", "reference": "ae42a47d7fecc1fbb7277b2f2d84c37a33edc3b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matthiasmullie/minify/zipball/ae42a47d7fecc1fbb7277b2f2d84c37a33edc3b1", "reference": "ae42a47d7fecc1fbb7277b2f2d84c37a33edc3b1", "shasum": ""}, "require": {"ext-pcre": "*", "matthiasmullie/path-converter": "~1.1", "php": ">=5.3.0"}, "require-dev": {"friendsofphp/php-cs-fixer": ">=2.0", "matthiasmullie/scrapbook": ">=1.3", "phpunit/phpunit": ">=4.8", "squizlabs/php_codesniffer": ">=3.0"}, "suggest": {"psr/cache-implementation": "Cache implementation to use with Minify::cache"}, "bin": ["bin/minifycss", "bin/minifyjs"], "type": "library", "autoload": {"psr-4": {"MatthiasMullie\\Minify\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.mullie.eu", "role": "Developer"}], "description": "CSS & JavaScript minifier, in PHP. Removes whitespace, strips comments, combines files (incl. @import statements and small assets in CSS files), and optimizes/shortens a few common programming patterns.", "homepage": "https://github.com/matthiasmullie/minify", "keywords": ["JS", "css", "javascript", "minifier", "minify"], "support": {"issues": "https://github.com/matthiasmullie/minify/issues", "source": "https://github.com/matthiasmullie/minify/tree/1.3.71"}, "funding": [{"url": "https://github.com/matthiasmullie", "type": "github"}], "time": "2023-04-25T20:33:03+00:00"}, {"name": "mat<PERSON><PERSON><PERSON><PERSON>/path-converter", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/matthiasmullie/path-converter.git", "reference": "e7d13b2c7e2f2268e1424aaed02085518afa02d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matthiasmullie/path-converter/zipball/e7d13b2c7e2f2268e1424aaed02085518afa02d9", "reference": "e7d13b2c7e2f2268e1424aaed02085518afa02d9", "shasum": ""}, "require": {"ext-pcre": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "autoload": {"psr-4": {"MatthiasMullie\\PathConverter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.mullie.eu", "role": "Developer"}], "description": "Relative path converter", "homepage": "http://github.com/matthiasmullie/path-converter", "keywords": ["converter", "path", "paths", "relative"], "support": {"issues": "https://github.com/matthiasmullie/path-converter/issues", "source": "https://github.com/matthiasmullie/path-converter/tree/1.1.3"}, "time": "2019-02-05T23:41:09+00:00"}, {"name": "nette/application", "version": "v3.1.14", "source": {"type": "git", "url": "https://github.com/nette/application.git", "reference": "0729ede7e66fad642046a3eb670d368845272573"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/application/zipball/0729ede7e66fad642046a3eb670d368845272573", "reference": "0729ede7e66fad642046a3eb670d368845272573", "shasum": ""}, "require": {"nette/component-model": "^3.0", "nette/http": "^3.0.2", "nette/routing": "^3.0.5", "nette/utils": "^3.2.1 || ~4.0.0", "php": ">=7.2"}, "conflict": {"latte/latte": "<2.7.1 || >=3.0.0 <3.0.8 || >=3.1", "nette/caching": "<3.1", "nette/di": "<3.0.7", "nette/forms": "<3.0", "nette/schema": "<1.2", "tracy/tracy": "<2.5"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "latte/latte": "^2.10.2 || ^3.0.3", "mockery/mockery": "^1.0", "nette/di": "^v3.0", "nette/forms": "^3.0", "nette/robot-loader": "^3.2", "nette/security": "^3.0", "nette/tester": "^2.3.1", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.6"}, "suggest": {"latte/latte": "Allows using Latte in templates", "nette/forms": "Allows to use Nette\\Application\\UI\\Form"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🏆 Nette Application: a full-stack component-based MVC kernel for PHP that helps you write powerful and modern web applications. Write less, have cleaner code and your work will bring you joy.", "homepage": "https://nette.org", "keywords": ["Forms", "component-based", "control", "framework", "mvc", "mvp", "nette", "presenter", "routing", "seo"], "support": {"issues": "https://github.com/nette/application/issues", "source": "https://github.com/nette/application/tree/v3.1.14"}, "time": "2023-10-09T02:45:43+00:00"}, {"name": "nette/bootstrap", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/nette/bootstrap.git", "reference": "eeb1c9dc9f1391bd03aeeb6cc0e456ec9b247f5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/bootstrap/zipball/eeb1c9dc9f1391bd03aeeb6cc0e456ec9b247f5c", "reference": "eeb1c9dc9f1391bd03aeeb6cc0e456ec9b247f5c", "shasum": ""}, "require": {"nette/di": "^3.1", "nette/utils": "^3.2.1 || ^4.0", "php": "8.0 - 8.3"}, "conflict": {"tracy/tracy": "<2.6"}, "require-dev": {"latte/latte": "^2.8 || ^3.0", "nette/application": "^3.1", "nette/caching": "^3.0", "nette/database": "^3.0", "nette/forms": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0 || ^4.0", "nette/robot-loader": "^3.0 || ^4.0", "nette/safe-stream": "^2.2", "nette/security": "^3.0", "nette/tester": "^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"nette/robot-loader": "to use Configurator::createRobotLoader()", "tracy/tracy": "to use Configurator::enableTracy()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🅱  Nette Bootstrap: the simple way to configure and bootstrap your Nette application.", "homepage": "https://nette.org", "keywords": ["bootstrapping", "configurator", "nette"], "support": {"issues": "https://github.com/nette/bootstrap/issues", "source": "https://github.com/nette/bootstrap/tree/v3.2.1"}, "time": "2023-09-23T01:12:54+00:00"}, {"name": "nette/caching", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/nette/caching.git", "reference": "6821d74c1db82c493c02c47f6485022d79b63176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/caching/zipball/6821d74c1db82c493c02c47f6485022d79b63176", "reference": "6821d74c1db82c493c02c47f6485022d79b63176", "shasum": ""}, "require": {"nette/finder": "^2.4 || ^3.0", "nette/utils": "^3.2 || ~4.0.0", "php": "8.0 - 8.3"}, "require-dev": {"latte/latte": "^2.11 || ^3.0", "nette/di": "^3.1 || ^4.0", "nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-pdo_sqlite": "to use SQLiteStorage or SQLiteJournal"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⏱ Nette Caching: library with easy-to-use API and many cache backends.", "homepage": "https://nette.org", "keywords": ["cache", "journal", "memcached", "nette", "sqlite"], "support": {"issues": "https://github.com/nette/caching/issues", "source": "https://github.com/nette/caching/tree/v3.2.3"}, "time": "2023-09-26T11:12:20+00:00"}, {"name": "nette/component-model", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/nette/component-model.git", "reference": "9d97c0e1916bbf8e306283ab187834501fd4b1f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/component-model/zipball/9d97c0e1916bbf8e306283ab187834501fd4b1f5", "reference": "9d97c0e1916bbf8e306283ab187834501fd4b1f5", "shasum": ""}, "require": {"nette/utils": "^2.5 || ^3.0 || ~4.0.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⚛ Nette Component Model", "homepage": "https://nette.org", "keywords": ["components", "nette"], "support": {"issues": "https://github.com/nette/component-model/issues", "source": "https://github.com/nette/component-model/tree/v3.0.3"}, "time": "2023-01-09T20:16:05+00:00"}, {"name": "nette/database", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/nette/database.git", "reference": "39f98a8c5a0e67d30424ca199e0630b678bcecd5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/database/zipball/39f98a8c5a0e67d30424ca199e0630b678bcecd5", "reference": "39f98a8c5a0e67d30424ca199e0630b678bcecd5", "shasum": ""}, "require": {"ext-pdo": "*", "nette/caching": "^3.2", "nette/utils": "^4.0", "php": "8.1 - 8.3"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "mockery/mockery": "^1.6", "nette/di": "^3.1", "nette/tester": "^2.5", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💾 Nette Database: layer with a familiar PDO-like API but much more powerful. Building queries, advanced joins, drivers for MySQL, PostgreSQL, SQLite, MS SQL Server and Oracle.", "homepage": "https://nette.org", "keywords": ["database", "mssql", "mysql", "nette", "notorm", "oracle", "pdo", "postgresql", "queries", "sqlite"], "support": {"issues": "https://github.com/nette/database/issues", "source": "https://github.com/nette/database/tree/v3.2.0"}, "time": "2023-12-14T20:00:58+00:00"}, {"name": "nette/di", "version": "v3.1.8", "source": {"type": "git", "url": "https://github.com/nette/di.git", "reference": "adf475076dae08109dd0d57b1a48668d1d1bedf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/di/zipball/adf475076dae08109dd0d57b1a48668d1d1bedf0", "reference": "adf475076dae08109dd0d57b1a48668d1d1bedf0", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/neon": "^3.3 || ^4.0", "nette/php-generator": "^3.5.4 || ^4.0", "nette/robot-loader": "^3.2 || ~4.0.0", "nette/schema": "^1.2.5", "nette/utils": "^3.2.5 || ~4.0.0", "php": "7.2 - 8.3"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💎 Nette Dependency Injection Container: Flexible, compiled and full-featured DIC with perfectly usable autowiring and support for all new PHP features.", "homepage": "https://nette.org", "keywords": ["compiled", "di", "dic", "factory", "ioc", "nette", "static"], "support": {"issues": "https://github.com/nette/di/issues", "source": "https://github.com/nette/di/tree/v3.1.8"}, "time": "2023-11-03T00:12:52+00:00"}, {"name": "nette/finder", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/nette/finder.git", "reference": "027395c638637de95c8e9fad49a7c51249404ed2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/finder/zipball/027395c638637de95c8e9fad49a7c51249404ed2", "reference": "027395c638637de95c8e9fad49a7c51249404ed2", "shasum": ""}, "require": {"nette/utils": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔍 Nette Finder: find files and directories with an intuitive API.", "homepage": "https://nette.org", "keywords": ["filesystem", "glob", "iterator", "nette"], "support": {"issues": "https://github.com/nette/finder/issues", "source": "https://github.com/nette/finder/tree/v3.0.0"}, "time": "2022-12-14T17:05:54+00:00"}, {"name": "nette/forms", "version": "v3.1.15", "source": {"type": "git", "url": "https://github.com/nette/forms.git", "reference": "f373bcd5ea7a33672fa96035d4bf3110ab66ee44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/forms/zipball/f373bcd5ea7a33672fa96035d4bf3110ab66ee44", "reference": "f373bcd5ea7a33672fa96035d4bf3110ab66ee44", "shasum": ""}, "require": {"nette/component-model": "^3.0", "nette/http": "^3.1", "nette/utils": "^3.2.5 || ~4.0.0", "php": "7.2 - 8.3"}, "conflict": {"latte/latte": ">=3.0.0 <3.0.12 || >=3.1"}, "require-dev": {"latte/latte": "^2.10.2 || ^3.0.12", "nette/application": "^3.0", "nette/di": "^3.0", "nette/tester": "^2.4", "phpstan/phpstan-nette": "^1", "tracy/tracy": "^2.9"}, "suggest": {"ext-intl": "to use date/time controls"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📝 Nette Forms: generating, validating and processing secure forms in PHP. Handy API, fully customizable, server & client side validation and mature design.", "homepage": "https://nette.org", "keywords": ["Forms", "bootstrap", "csrf", "javascript", "nette", "validation"], "support": {"issues": "https://github.com/nette/forms/issues", "source": "https://github.com/nette/forms/tree/v3.1.15"}, "time": "2024-01-21T22:22:16+00:00"}, {"name": "nette/http", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/nette/http.git", "reference": "54d79711ff3a8dfd86201e3bdbdd6972177ea20b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/http/zipball/54d79711ff3a8dfd86201e3bdbdd6972177ea20b", "reference": "54d79711ff3a8dfd86201e3bdbdd6972177ea20b", "shasum": ""}, "require": {"nette/utils": "^3.2.1 || ~4.0.0", "php": "7.2 - 8.3"}, "conflict": {"nette/di": "<3.0.3", "nette/schema": "<1.2"}, "require-dev": {"nette/di": "^3.0", "nette/security": "^3.0", "nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"ext-fileinfo": "to detect type of uploaded files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🌐 Nette Http: abstraction for HTTP request, response and session. Provides careful data sanitization and utility for URL and cookies manipulation.", "homepage": "https://nette.org", "keywords": ["cookies", "http", "nette", "proxy", "request", "response", "security", "session", "url"], "support": {"issues": "https://github.com/nette/http/issues", "source": "https://github.com/nette/http/tree/v3.2.3"}, "time": "2023-11-02T02:43:54+00:00"}, {"name": "nette/mail", "version": "v4.0.2", "source": {"type": "git", "url": "https://github.com/nette/mail.git", "reference": "c0b81124284bee573ee968de98fe3dcf2c2a9b5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/mail/zipball/c0b81124284bee573ee968de98fe3dcf2c2a9b5e", "reference": "c0b81124284bee573ee968de98fe3dcf2c2a9b5e", "shasum": ""}, "require": {"ext-iconv": "*", "nette/utils": "^4.0", "php": "8.0 - 8.3"}, "require-dev": {"nette/di": "^3.1 || ^4.0", "nette/tester": "^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"ext-fileinfo": "to detect type of attached files", "ext-openssl": "to use Nette\\Mail\\DkimSigner"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📧 Nette Mail: handy email creation and transfer library for PHP with both text and MIME-compliant support.", "homepage": "https://nette.org", "keywords": ["mail", "mailer", "mime", "nette", "smtp"], "support": {"issues": "https://github.com/nette/mail/issues", "source": "https://github.com/nette/mail/tree/v4.0.2"}, "time": "2023-10-02T20:59:33+00:00"}, {"name": "nette/neon", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "457bfbf0560f600b30d9df4233af382a478bb44d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/457bfbf0560f600b30d9df4233af382a478bb44d", "reference": "457bfbf0560f600b30d9df4233af382a478bb44d", "shasum": ""}, "require": {"ext-json": "*", "php": "8.0 - 8.3"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.7"}, "bin": ["bin/neon-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍸 Nette NEON: encodes and decodes NEON file format.", "homepage": "https://ne-on.org", "keywords": ["export", "import", "neon", "nette", "yaml"], "support": {"issues": "https://github.com/nette/neon/issues", "source": "https://github.com/nette/neon/tree/v3.4.1"}, "time": "2023-09-27T08:59:11+00:00"}, {"name": "nette/nette", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/nette/nette.git", "reference": "0274f0b0cd8456297604ac5c40df6ace2d8e5910"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/nette/zipball/0274f0b0cd8456297604ac5c40df6ace2d8e5910", "reference": "0274f0b0cd8456297604ac5c40df6ace2d8e5910", "shasum": ""}, "require": {"latte/latte": "^2.10 || ^3.0", "nette/application": "^3.1", "nette/bootstrap": "^3.1", "nette/caching": "^3.1", "nette/database": "^3.1", "nette/di": "^3.1", "nette/finder": "^2.6 || ^3.0", "nette/forms": "^3.1", "nette/http": "^3.2", "nette/mail": "^3.1 || ^4.0", "nette/php-generator": "^3.6 || ^4.0", "nette/robot-loader": "^3.4 || ^4.0", "nette/safe-stream": "^2.5 || ^3.0", "nette/security": "^3.1", "nette/tokenizer": "^3.1", "nette/utils": "^3.2 || ^4.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["Nette/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "👪 Nette Framework - innovative framework for fast and easy development of secured web applications in PHP (metapackage)", "homepage": "https://nette.org", "keywords": ["framework", "metapackage", "mvc"], "support": {"issues": "https://github.com/nette/nette/issues", "source": "https://github.com/nette/nette/tree/v3.2.1"}, "time": "2023-11-05T22:07:52+00:00"}, {"name": "nette/php-generator", "version": "v4.1.3", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "08ab9bff22ae34fe4e1d2fe8ba16b3770ea2459f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/08ab9bff22ae34fe4e1d2fe8ba16b3770ea2459f", "reference": "08ab9bff22ae34fe4e1d2fe8ba16b3770ea2459f", "shasum": ""}, "require": {"nette/utils": "^3.2.9 || ^4.0", "php": "8.0 - 8.3"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.4", "nikic/php-parser": "^4.18 || ^5.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"nikic/php-parser": "to use ClassType::from(withBodies: true) & ClassType::fromCode()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.3 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v4.1.3"}, "time": "2024-01-18T17:44:20+00:00"}, {"name": "nette/robot-loader", "version": "v4.0.1", "source": {"type": "git", "url": "https://github.com/nette/robot-loader.git", "reference": "3a947efaff55d48e8cdba5b338bf3a4b708a624a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/robot-loader/zipball/3a947efaff55d48e8cdba5b338bf3a4b708a624a", "reference": "3a947efaff55d48e8cdba5b338bf3a4b708a624a", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/utils": "^4.0", "php": "8.0 - 8.3"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍀 Nette RobotLoader: high performance and comfortable autoloader that will search and autoload classes within your application.", "homepage": "https://nette.org", "keywords": ["autoload", "class", "interface", "nette", "trait"], "support": {"issues": "https://github.com/nette/robot-loader/issues", "source": "https://github.com/nette/robot-loader/tree/v4.0.1"}, "time": "2023-09-26T18:09:36+00:00"}, {"name": "nette/routing", "version": "v3.1.0", "source": {"type": "git", "url": "https://github.com/nette/routing.git", "reference": "f7419bc147164106cb03b3d331c85aff6cb81fc3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/routing/zipball/f7419bc147164106cb03b3d331c85aff6cb81fc3", "reference": "f7419bc147164106cb03b3d331c85aff6cb81fc3", "shasum": ""}, "require": {"nette/http": "^3.2 || ~4.0.0", "nette/utils": "^4.0", "php": "8.1 - 8.3"}, "require-dev": {"nette/tester": "^2.5", "phpstan/phpstan": "^1", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Routing: two-ways URL conversion", "homepage": "https://nette.org", "keywords": ["nette"], "support": {"issues": "https://github.com/nette/routing/issues", "source": "https://github.com/nette/routing/tree/v3.1.0"}, "time": "2024-01-21T21:13:45+00:00"}, {"name": "nette/safe-stream", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/nette/safe-stream.git", "reference": "b9a275f7f2517cacac6ab4360a73722340478bce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/safe-stream/zipball/b9a275f7f2517cacac6ab4360a73722340478bce", "reference": "b9a275f7f2517cacac6ab4360a73722340478bce", "shasum": ""}, "require": {"php": "8.0 - 8.3"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/loader.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette SafeStream: provides isolation for thread safe manipulation with files via native PHP functions.", "homepage": "https://nette.org", "keywords": ["atomic", "filesystem", "isolation", "nette", "safe", "thread safe"], "support": {"issues": "https://github.com/nette/safe-stream/issues", "source": "https://github.com/nette/safe-stream/tree/v3.0.1"}, "time": "2023-08-05T18:54:54+00:00"}, {"name": "nette/schema", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "a6d3a6d1f545f01ef38e60f375d1cf1f4de98188"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/a6d3a6d1f545f01ef38e60f375d1cf1f4de98188", "reference": "a6d3a6d1f545f01ef38e60f375d1cf1f4de98188", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.1 - 8.3"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.3.0"}, "time": "2023-12-11T11:54:22+00:00"}, {"name": "nette/security", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/nette/security.git", "reference": "fe89d52697036fb2e14835dfb46b696d28a9ebf6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/security/zipball/fe89d52697036fb2e14835dfb46b696d28a9ebf6", "reference": "fe89d52697036fb2e14835dfb46b696d28a9ebf6", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.1 - 8.3"}, "conflict": {"nette/di": "<3.0-stable", "nette/http": "<3.1.3"}, "require-dev": {"mockery/mockery": "^1.5", "nette/di": "^3.1", "nette/http": "^3.2", "nette/tester": "^2.5", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔑 Nette Security: provides authentication, authorization and a role-based access control management via ACL (Access Control List)", "homepage": "https://nette.org", "keywords": ["Authentication", "acl", "authorization", "nette"], "support": {"issues": "https://github.com/nette/security/issues", "source": "https://github.com/nette/security/tree/v3.2.0"}, "time": "2024-01-21T21:33:53+00:00"}, {"name": "nette/tokenizer", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/nette/tokenizer.git", "reference": "370c5e4e2e10eb4d3e406d3a90526f821de98190"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tokenizer/zipball/370c5e4e2e10eb4d3e406d3a90526f821de98190", "reference": "370c5e4e2e10eb4d3e406d3a90526f821de98190", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"nette/tester": "~2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "<PERSON><PERSON>", "homepage": "https://nette.org", "support": {"source": "https://github.com/nette/tokenizer/tree/v3.1.1"}, "abandoned": true, "time": "2022-02-09T22:28:54+00:00"}, {"name": "nette/utils", "version": "v4.0.4", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "shasum": ""}, "require": {"php": ">=8.0 <8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.4"}, "time": "2024-01-17T16:50:36+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "58c3f47f650c94ec05a151692652a868995d2938"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/58c3f47f650c94ec05a151692652a868995d2938", "reference": "58c3f47f650c94ec05a151692652a868995d2938", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2022-06-14T06:56:20+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.35", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "4b1827beabce71953ca479485c0ae9c51287f2fe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/4b1827beabce71953ca479485c0ae9c51287f2fe", "reference": "4b1827beabce71953ca479485c0ae9c51287f2fe", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.35"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2023-12-29T01:59:53+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "symfony/console", "version": "v6.4.6", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "a2708a5da5c87d1d0d52937bdeac625df659e11f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/a2708a5da5c87d1d0d52937bdeac625df659e11f", "reference": "a2708a5da5c87d1d0d52937bdeac625df659e11f", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-29T19:07:53+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/7c3aff79d10325257a001fcf92d991f24fc967cf", "reference": "7c3aff79d10325257a001fcf92d991f24fc967cf", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-23T14:45:45+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ef4d7e442ca910c4764bce785146269b30cb5fc4", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/32a9da87d7b3245e09ac426c83d334ae9f06f80f", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/bc45c394692b948b4d383a08d7753968bed9a83d", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/service-contracts", "version": "v3.4.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "11bbf19a0fb7b36345861e85c5768844c552906e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/11bbf19a0fb7b36345861e85c5768844c552906e", "reference": "11bbf19a0fb7b36345861e85c5768844c552906e", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.4.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-12-19T21:51:00+00:00"}, {"name": "symfony/string", "version": "v6.4.4", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "4e465a95bdc32f49cf4c7f07f751b843bbd6dcd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/4e465a95bdc32f49cf4c7f07f751b843bbd6dcd9", "reference": "4e465a95bdc32f49cf4c7f07f751b843bbd6dcd9", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/intl": "^6.2|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.4.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-02-01T13:16:41+00:00"}, {"name": "tracy/tracy", "version": "v2.10.5", "source": {"type": "git", "url": "https://github.com/nette/tracy.git", "reference": "86bdba4aa0f707d3f125933931d3df6e5c7aad79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tracy/zipball/86bdba4aa0f707d3f125933931d3df6e5c7aad79", "reference": "86bdba4aa0f707d3f125933931d3df6e5c7aad79", "shasum": ""}, "require": {"ext-json": "*", "ext-session": "*", "php": ">=8.0 <8.4"}, "conflict": {"nette/di": "<3.0"}, "require-dev": {"latte/latte": "^2.5", "nette/di": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0", "nette/tester": "^2.2", "nette/utils": "^3.0", "phpstan/phpstan": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.10-dev"}}, "autoload": {"files": ["src/Tracy/functions.php"], "classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "😎  Tracy: the addictive tool to ease debugging PHP code for cool developers. Friendly design, logging, profiler, advanced features like debugging AJAX calls or CLI support. You will love it.", "homepage": "https://tracy.nette.org", "keywords": ["Xdebug", "debug", "debugger", "nette", "profiler"], "support": {"issues": "https://github.com/nette/tracy/issues", "source": "https://github.com/nette/tracy/tree/v2.10.5"}, "time": "2023-10-16T21:54:18+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "dev", "stability-flags": {"contributte/deployer-extension": 20, "kubomikita/cms-core": 20, "kubomikita/form-factory": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": [], "platform-dev": [], "platform-overrides": {"php": "8.1"}, "plugin-api-version": "2.6.0"}