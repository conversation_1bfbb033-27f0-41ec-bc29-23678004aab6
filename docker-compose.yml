networks:
  benard:
  superkoders:
    external: false

volumes:
  db:

services:
  app:
    build:
      dockerfile: docker/app/Dockerfile
      context: .
    hostname: app
    container_name: benard_app
    ports:
      - '8080:80'
    networks:
      - benard
      - superkoders
    volumes:
      - .:/var/www/html
      - ./docker/app/php-xdebug-${SUPERADMIN_XDEBUG:-off}.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
      - ../kubomikita/form-factory:/var/www/html/vendor/kubomikita/form-factory
      - ../kubomikita/cms-core:/var/www/html/vendor/kubomikita/cms-core
    depends_on:
      - db

  adminer:
    image: adminer
    restart: always
    networks:
      - benard
    ports:
      - '8081:8080'
    environment:
      ADMINER_DEFAULT_SERVER: benard_db
      ADMINER_DEFAULT_DB: benard
      ADMINER_USER: benard
      ADMINER_PASSWORD: benard
  db:
    image: mariadb:10
    hostname: benard_db
    container_name: benard_db
    networks:
      - benard
    ports:
      - '3306:3306'
    volumes:
      - db:/var/lib/mysql
      - ./docker/db/custom.cnf:/etc/mysql/conf.d/custom.cnf
      - ./docker/db:/docker/db
      - ./docker/db/compare-db.sh:/docker/db/compare-db.sh
    environment:
      MARIADB_HOST: 'benard_db'
      MARIADB_ROOT_PASSWORD: 'root'
      MARIADB_DATABASE: 'benard'
      MARIADB_USER: 'benard'
      MARIADB_PASSWORD: 'benard'


  mailcatcher:
    image: dockage/mailcatcher
    hostname: benard_mailcatcher
    container_name: benard_mailcatcher
    networks:
      - benard
    ports:
      - '1080:1080'

  # run with: docker-compose --profile ngrok up -d
  ngrok:
    image: ngrok/ngrok
    container_name: ngrok
    networks:
      - benard
    ports:
      - '4040:4040'
    volumes:
      - ./docker/app/ngrok.yml:/etc/ngrok.yml
    command:
      - 'start'
      - '--all'
      - '--config'
      - '/etc/ngrok.yml'
    profiles:
      - ngrok
