services:

    router.routerFactory: App\RouterFactory
database:
    dsn: "mysql:host=%database.host%;dbname=%database.name%"
    user: %database.user%
    password: %database.password%
session:
    debugger: true


#forms:
#    service: CMSIzzy\Services\FormFactory

extensions:
    console: Contributte\Console\DI\ConsoleExtension(%consoleMode%)

console:
	name: "IzzyCms"
	version: '1.0'

search:
	commands:
		in: %appDir%/Console
		extends: Symfony\Component\Console\Command\Command