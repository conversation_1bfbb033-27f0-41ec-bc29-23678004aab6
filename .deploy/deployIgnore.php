<?php

$vendorDir = __DIR__ . '/../vendor';
$lockPath = __DIR__ . '/../composer.lock';

$existingPackages = [];
$ignored = [];

foreach (glob($vendorDir . '/*/*', GLOB_ONLYDIR) as $dir) {
	$relPath = str_replace($vendorDir . '/', '', $dir);
	$existingPackages[] = str_replace(DIRECTORY_SEPARATOR, '/', $relPath);
}

$lockData = json_decode(file_get_contents($lockPath), true);
$installedPackages = array_map(
	fn($pkg) => $pkg['name'],
	array_merge($lockData['packages'] ?? [], $lockData['packages-dev'] ?? [])
);

// nájdi tie, ktoré nie sú v locku
$ignored = array_values(array_diff($existingPackages, $installedPackages));

return [
	'parameters' => [
		'deployment' => [
			'ignore' => array_map(fn($pkg) => 'commerce/' . "vendor/$pkg", $ignored),
		]
	],

];