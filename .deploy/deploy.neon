services:
    - \DeployListener
extensions:
    deployer: Contributte\Deployer\DI\DeployerExtension
parameters:
    email: <EMAIL>
    deployment:
        ignore:
        	- /.deploy
        	- deploy.*
        	- /.idea
        	- /__nenahrat
        	- /temp/*
        	- /log/*
        	- *.production
        	- /www/mysql.php
        	- /www/test.php
        	- /www/tr.php
        	- /www/imgcache/*
        	- /app/vendor/kubomikita/*/.*
        	- /app/vendor/*/*/tests
        	- /www/uploads/*
        	- /docker
        	- /docker-compose.yml
        	- /Makefile
        	- /config/config.docker.neon
deployer:
    config:
        mode: run
        logFile: %appDir%/log/deployer.log
        tempDir: %tempDir%
        colors: true
    sections:
        benard.sk:
            #remote: "sftp://benard.jakubmikita.sk:Tw5d-wK]R6@37.9.175.156"
            remote: "sftp://deploy.benardreality.sk:Ha4eRYaC3>@37.9.175.189"
            local: %wwwDir%
            testMode: false
            ignore: %deployment.ignore%
            after:
                #- [@\DeployListener, onAfter]
                #- [@Kubomikita\DeployListener, testAfter]
                #- [@Kubomikita\DeployListener, testAfterTwo]
            before:
                #- [@Kubomikita\DeployListener, onBefore]
                #- [@Kubomikita\DeployListener, startTests]
            purge:
                - temp/cache
includes:
    - deployIgnore.php