<?php


use Contributte\Deployer\Config\Config;
use Contributte\Deployer\Config\Section;
use Contributte\Deployer\Listeners\AfterListener;
use Contributte\Deployer\Listeners\BeforeListener;
use Contributte\Deployer\Utils\System;
use Deployment\Deployer;
use Deployment\Logger;
use Deployment\Server;

class DeployListener implements AfterListener, BeforeListener {

	public function testAfter(Config $config, Section $section, Server $server, Logger $logger, Deployer $deployer) :void {
		$logger->log("test after","aqua");
	}

	private function loadConfig($webname) {
		$this->searchFor = [
			//"ErrorDocument 404 /".$webname."/",
			"RewriteBase /".$webname."/",
			//"RewriteBase /".$webname."/imgcache/"
			//"RewriteRule ^imgcache/(.*)$ /".$webname."/imgcache/$1",
			//"RewriteRule ^imgs/(.*)$ /".$webname."/imgs/$1",
			//"RewriteRule ^imgi/(.*)$ /".$webname."/imgi/$1",
			//"RewriteRule ^imgc/(.*)$ /".$webname."/imgc/$1",
			//"RewriteRule ^img.php$ /".$webname."/img.php"
		];
		$this->replaceFor = [
			//"ErrorDocument 404 /",
			"RewriteBase /",
			//"RewriteBase /imgcache/"
			//"RewriteRule ^imgcache/(.*)$ /imgcache/$1",
			//"RewriteRule ^imgs/(.*)$ /imgs/$1",
			//"RewriteRule ^imgi/(.*)$ /imgi/$1",
			//"RewriteRule ^imgc/(.*)$ /imgc/$1",
			//"RewriteRule ^img.php$ /img.php"
		];

		$this->files = [".htaccess"/*,"imgcache/.htaccess"*/,"admin/.htaccess"/*"commerce/manager/.htaccess"*/];
	}

	public function onAfter(Config $config, Section $section, Server $server, Logger $logger, Deployer $deployer) :void
	{
		$webname = $section->getName();
		$this->loadConfig($webname);
		foreach($this->files as $file){
			$p = file_get_contents($section->getLocal()."/".$file);
			$new = str_replace($this->searchFor,$this->replaceFor,$p);
			file_put_contents($section->getLocal()."/".$file.".production",$new);
			$server->writeFile($section->getLocal()."/".$file.".production",$server->getDir()."/".$file);
			//unlink($section->getLocal()."/".$file.".production");
			$logger->log("File: ".$file." changed to production and uploaded.");
		}
	}
	public function onBefore(Config $config, Section $section, Server $server, Logger $logger, Deployer $deployer) :void {
		$logger->log("OnBefore listener","navy");
		//System::run(sprintf('composer update --no-dev --prefer-dist --optimize-autoloader -d %s', ""), $return);
		//$logger->log($return);
	}
	public function startTests(Config $config, Section $section, Server $server, Logger $logger, Deployer $deployer) :void {
		$logger->log("Starting UNIT tests", "navy");
		$logger->log("---------------------------------","navy");
		System::run("php engine/libs/nette/tester/src/tester.php engine/tests -C --colors 1",$return);
		if($return > 0){
			$logger->log("============================================","red");
			$logger->log("=========== UNIT TEST FAILED ===============","red");
			$logger->log("============================================","red");
			exit;
		} else {
			$logger->log("UNIT TEST PASSED","lime");
			$logger->log("----------------","lime");
		}
	}
}