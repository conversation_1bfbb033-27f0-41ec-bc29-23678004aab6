FROM php:8.2-apache
ENV LOCAL_DOCKER 1

# Set memory limit
RUN echo "memory_limit=3G" > /usr/local/etc/php/conf.d/memory-limit.ini

COPY --from=mlocati/php-extension-installer /usr/bin/install-php-extensions /usr/local/bin
RUN IPE_GD_WITHOUTAVIF=1 install-php-extensions gd intl curl mbstring mysqli xdebug zip ssh2 pdo_mysql
# ^ use gd without avif, it currently takes ages to build (see https://github.com/mlocati/docker-php-extension-installer/issues/514)

# Install packages
RUN apt-get update && \
    apt-get install -y \
    	vim \
        sendmail

RUN docker-php-ext-install exif
#RUN install-php-extensions amqp


RUN sed -i '/#!\/bin\/sh/aservice sendmail restart' /usr/local/bin/docker-php-entrypoint
RUN sed -i '/#!\/bin\/sh/aecho "$(hostname -i)\t$(hostname) $(hostname).localhost" >> /etc/hosts' /usr/local/bin/docker-php-entrypoint

# And clean up the image
RUN rm -rf /var/lib/apt/lists/*
# End of sendmail installation

ENV APACHE_DOCUMENT_ROOT /var/www/html/www
RUN sed -ri -e 's!/var/www/html!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/sites-available/*.conf
RUN sed -ri -e 's!/var/www/!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf

RUN echo "ServerName horti.test" >> /etc/apache2/apache2.conf

#COPY ./docker/app/supervisorctl-worker /etc/supervisor/conf.d/dev.conf
#RUN sed -i '/#!\/bin\/sh/aservice supervisor start' /usr/local/bin/docker-php-entrypoint

RUN a2enmod rewrite
