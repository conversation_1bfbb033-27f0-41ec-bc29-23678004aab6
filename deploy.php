<?php

define('APP_DIR', __DIR__);
define('WWW_DIR', APP_DIR."/www");
define('LOG_DIR', APP_DIR . '/log');
define('LIBS_DIR', APP_DIR . '/vendor');
define('TEMP_DIR', APP_DIR . '/temp/deploy');

require __DIR__ . '/vendor/autoload.php';
require __DIR__ . "/.deploy/DeployListener.php";
# Configurator
$configurator = new Nette\Configurator;
$configurator->setDebugMode(TRUE);
$configurator->enableTracy(LOG_DIR);
$configurator->setTempDirectory(TEMP_DIR);
$configurator->createRobotLoader()
             ->addDirectory(APP_DIR)
             ->register();

# Configs
$configurator->addConfig(APP_DIR . '/.deploy/deploy.neon');

# Create DI Container
$container = $configurator->createContainer();
//print_r($container->parameters);exit;
# Create Deploy Manager
$dm = $container->getByType('Contributte\Deployer\Manager');
$dm->deploy();