{"minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "path", "url": "../kubomikita/cms-core", "options": {"symlink": true}}, {"type": "path", "url": "../kubomikita/form-factory", "options": {"symlink": true}}, {"type": "vcs", "url": "https://github.com/kubomikita/deployer-extension.git"}], "require": {"contributte/deployer-extension": "dev-advholding", "kubomikita/cms-core": "dev-master", "kubomikita/form-factory": "dev-php8.1", "contributte/console": "^0.10.1"}, "config": {"vendor-dir": "vendor/", "platform": {"php": "8.1"}}, "autoload": {"psr-4": {"App\\": ["app/"]}, "files": ["app/functions.php"]}}