/**
 * CSS štýly pre lazy loading obr<PERSON><PERSON>kov
 * Jednoduchý fallback pre star<PERSON><PERSON>
 */

/* <PERSON><PERSON><PERSON><PERSON><PERSON>ýly pre lazy loading obr<PERSON>zky (fallback) */
.lazy {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    background-color: #f8f9fa;
    background-image: linear-gradient(90deg, #f8f9fa 25%, rgba(255,255,255,0) 50%, #f8f9fa 75%);
    background-size: 200% 100%;
    animation: lazy-loading 1.5s infinite;
}

/* Animácia pre načítavanie */
@keyframes lazy-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Načítaný obrázok */
.lazy-loaded {
    opacity: 1;
    background: none;
    animation: none;
}

/* Responzívne obrázky */
.lazy,
.lazy-loaded {
    max-width: 100%;
    height: auto;
}

/* Pre tmavý režim */
@media (prefers-color-scheme: dark) {
    .lazy {
        background-color: #343a40;
        background-image: linear-gradient(90deg, #343a40 25%, rgba(255,255,255,0.1) 50%, #343a40 75%);
    }
}

/* Pre pomalé pripojenie - vypni animácie */
@media (prefers-reduced-motion: reduce) {
    .lazy {
        animation: none;
        background: #f8f9fa;
    }
}

/* Print štýly */
@media print {
    .lazy {
        opacity: 1;
        background: none;
        animation: none;
    }
}
