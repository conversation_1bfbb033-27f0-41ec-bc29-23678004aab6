/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> lazy loading pre obr<PERSON><PERSON><PERSON>
 * Fallback pre prehlia<PERSON><PERSON><PERSON>, k<PERSON><PERSON> nepodporujú natívny loading="lazy"
 */

(function() {
    'use strict';

    // Skontroluj podporu natívneho lazy loading
    if ('loading' in HTMLImageElement.prototype) {
        // Prehliadač podporuje natívny lazy loading, netreba nič robiť
        console.log('Native lazy loading is supported');
        return;
    }

    console.log('Native lazy loading not supported, using fallback');

    // Fallback pre staršie prehliadače
    let lazyImages = [];
    let imageObserver;

    function loadImage(img) {
        img.src = img.dataset.src;
        img.classList.remove('lazy');
        img.classList.add('lazy-loaded');
    }

    function handleIntersection(entries, observer) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                loadImage(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }

    function initLazyLoading() {
        lazyImages = document.querySelectorAll('img[loading="lazy"]');

        if ('IntersectionObserver' in window) {
            imageObserver = new IntersectionObserver(handleIntersection, {
                rootMargin: '50px 0px'
            });

            lazyImages.forEach(function(img) {
                // Pre fallback presuň src do data-src a nastav placeholder
                if (!img.dataset.src && img.src) {
                    img.dataset.src = img.src;
                    img.src = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E';
                }
                img.classList.add('lazy');
                imageObserver.observe(img);
            });
        } else {
            // Fallback pre veľmi staré prehliadače
            function loadImagesInViewport() {
                lazyImages.forEach(function(img) {
                    if (img.getBoundingClientRect().top < window.innerHeight + 50) {
                        if (!img.dataset.src && img.getAttribute('loading') === 'lazy') {
                            // Obrázok už má správny src, len odstráň loading atribút
                            img.removeAttribute('loading');
                        } else if (img.dataset.src) {
                            loadImage(img);
                        }
                    }
                });
            }

            // Pre veľmi staré prehliadače načítaj všetky obrázky okamžite
            lazyImages.forEach(function(img) {
                img.removeAttribute('loading');
            });
        }
    }

    // Inicializuj po načítaní DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initLazyLoading);
    } else {
        initLazyLoading();
    }

    // Pridaj možnosť manuálnej inicializácie pre dynamicky pridané obrázky
    window.initLazyImages = function() {
        if (imageObserver) {
            const newImages = document.querySelectorAll('img[loading="lazy"]:not(.lazy):not(.lazy-loaded)');
            newImages.forEach(function(img) {
                if (!img.dataset.src && img.src) {
                    img.dataset.src = img.src;
                    img.src = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E';
                }
                img.classList.add('lazy');
                imageObserver.observe(img);
            });
        }
    };

})();
