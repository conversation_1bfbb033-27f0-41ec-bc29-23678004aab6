<?php
include 'app/bootstrap_old.php';

header("Content-type: text/xml; charset=utf-8");

?>
<<?php ?>?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
<?php




$all_pages = Page::fetchAll("pages_location_id != 'none' AND module_id != 'NULL' AND active = '1'");
$all_lg = Language::fetchAll();
$base = 'http://'.$_SERVER["SERVER_NAME"].Core::$conf["webRel"];

foreach ($all_lg as $the_lg) {

	$lg = $the_lg->id==Core::$conf["defaultLanguage"]?"":$the_lg->id."/";
	
	echo '
		<url>
		  <loc>'. $base.$lg .'</loc>
		</url>';

	foreach ($all_pages as $prod) {

		// Content
		if($prod->module_id == 'content') {

			echo '
				<url>
				  <loc>'. $base.$lg.$prod->text($the_lg->id)->seo_name .'</loc>
				</url>
				';		

		// Category	
		} elseif($prod->module_id == 'category') {
			$all_cont = Content::fetchAll("hidden='0' AND category LIKE '%[$prod->module_child_id]%'");

			echo '
				<url>
				  <loc>'. $base.$lg.$prod->text($the_lg->id)->seo_name.'</loc>
				</url>
				';
			foreach ($all_cont as $C) {	
				echo '
					<url>
					  <loc>'. $base.$lg.$prod->text($the_lg->id)->seo_name."/".$C->text($lg)->seo_name .'</loc>
					</url>
					';
			}
		
		// Gallery
		} elseif($prod->module_id == 'gallery') {
			echo '
				<url>
				  <loc>'. $base.$lg.$prod->text($the_lg->id)->seo_name .'</loc>
				</url>
				';
		}
	}
}

?>
</urlset>