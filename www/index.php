<?php
/** @var \Nette\DI\Container $container */
//include __DIR__ . "/../app/bootstrap_old.php";
//$container->getByType(\CMSIzzy\Application::class)->run();

const APP_DIR = __DIR__ . '/../app';
const WWW_DIR = __DIR__;
const CORE_DIR = APP_DIR . "/core";
const TEMP_DIR = APP_DIR . "/../temp";
const LOG_DIR = APP_DIR . '/../log';

include_once __DIR__.'/../vendor/autoload.php';
\App\Bootstrap::boot()->createContainer(APP_DIR.'/../config/services.neon')->getByType(\CMSIzzy\Application::class)->run();

exit;
