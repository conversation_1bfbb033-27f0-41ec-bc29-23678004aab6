Options -Indexes

DirectoryIndex index.php

# cache files
<IfModule mod_expires.c>
	<Filesmatch "\.(jpg|jpeg|png|gif|js|css|svg|swf|ico|woff|mp3)$">
		ExpiresActive on
		ExpiresDefault "access plus 1 month"
	</Filesmatch>
</IfModule>

# enable Keep-Alive connections
<ifModule mod_headers.c>
	Header set Connection keep-alive
</ifModule>

<IfModule mod_rewrite.c>
	RewriteEngine on

	RewriteCond %{HTTP_HOST} ^benard.sk$ [NC]
	RewriteRule (.*) https://www.benard.sk/$1 [R=301,L]

	#RewriteCond %{HTTP:X-Forwarded-Proto} !=https
	#RewriteRule ^.*$ https://%{SERVER_NAME}%{REQUEST_URI} [L,R]

	RewriteBase /

	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteCond %{REQUEST_FILENAME} !-f



	# disallow internal files
	<Files ~ "\.gitignore|\.bowerrc|composer\.json|composer\.lock|\.dep">
		Require all denied
	</Files>
	SetEnvIF CF-Connecting-IP "************" my_networks
	SetEnvIF CF-Connecting-IP "*************" my_networks
	<Files ~ "adminer">
		Require all denied
		require ip *************
	</Files>



	RewriteCond %{HTTP_USER_AGENT} libwww-perl.*
	RewriteRule .* – [F,L]


	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule imgcache/(.*)$ index.php [L] #(?:_([a-z0-9]+?))?

	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule !\.(js|ico|gif|jpg|png|css|svg|rar|zip|txt|tar\.gz)$ index.php [L]

</IfModule>